const MODELS = {
  "accounts/fireworks/models/llama-v3p2-3b-instruct": {
    id: "accounts/fireworks/models/llama-v3p2-3b-instruct",
    organization: "Meta",
    name: "Llama 3.2 3B Instruct",
    maxLength: 131072,
  },
  "accounts/fireworks/models/llama-v3p2-1b-instruct": {
    id: "accounts/fireworks/models/llama-v3p2-1b-instruct",
    organization: "Meta",
    name: "Llama 3.2 1B Instruct",
    maxLength: 131072,
  },
  "accounts/fireworks/models/llama-v3p1-405b-instruct": {
    id: "accounts/fireworks/models/llama-v3p1-405b-instruct",
    organization: "Meta",
    name: "Llama 3.1 405B Instruct",
    maxLength: 131072,
  },
  "accounts/fireworks/models/llama-v3p1-70b-instruct": {
    id: "accounts/fireworks/models/llama-v3p1-70b-instruct",
    organization: "Meta",
    name: "Llama 3.1 70B Instruct",
    maxLength: 131072,
  },
  "accounts/fireworks/models/llama-v3p1-8b-instruct": {
    id: "accounts/fireworks/models/llama-v3p1-8b-instruct",
    organization: "Meta",
    name: "Llama 3.1 8B Instruct",
    maxLength: 131072,
  },
  "accounts/fireworks/models/llama-v3-70b-instruct": {
    id: "accounts/fireworks/models/llama-v3-70b-instruct",
    organization: "Meta",
    name: "Llama 3 70B Instruct",
    maxLength: 8192,
  },
  "accounts/fireworks/models/mixtral-8x22b-instruct": {
    id: "accounts/fireworks/models/mixtral-8x22b-instruct",
    organization: "mistralai",
    name: "Mixtral MoE 8x22B Instruct",
    maxLength: 65536,
  },
  "accounts/fireworks/models/mixtral-8x7b-instruct": {
    id: "accounts/fireworks/models/mixtral-8x7b-instruct",
    organization: "mistralai",
    name: "Mixtral MoE 8x7B Instruct",
    maxLength: 32768,
  },
  "accounts/fireworks/models/firefunction-v2": {
    id: "accounts/fireworks/models/firefunction-v2",
    organization: "Fireworks AI",
    name: "Firefunction V2",
    maxLength: 8192,
  },
  "accounts/fireworks/models/firefunction-v1": {
    id: "accounts/fireworks/models/firefunction-v1",
    organization: "Fireworks AI",
    name: "FireFunction V1",
    maxLength: 32768,
  },
  "accounts/fireworks/models/gemma2-9b-it": {
    id: "accounts/fireworks/models/gemma2-9b-it",
    organization: "Google",
    name: "Gemma 2 9B Instruct",
    maxLength: 8192,
  },
  "accounts/fireworks/models/llama-v3-70b-instruct-hf": {
    id: "accounts/fireworks/models/llama-v3-70b-instruct-hf",
    organization: "Hugging Face",
    name: "Llama 3 70B Instruct (HF version)",
    maxLength: 8192,
  },
  "accounts/fireworks/models/llama-v3-8b-instruct": {
    id: "accounts/fireworks/models/llama-v3-8b-instruct",
    organization: "Hugging Face",
    name: "Llama 3 8B Instruct",
    maxLength: 8192,
  },
  "accounts/fireworks/models/llama-v3-8b-instruct-hf": {
    id: "accounts/fireworks/models/llama-v3-8b-instruct-hf",
    organization: "Hugging Face",
    name: "Llama 3 8B Instruct (HF version)",
    maxLength: 8192,
  },
  "accounts/fireworks/models/mixtral-8x7b-instruct-hf": {
    id: "accounts/fireworks/models/mixtral-8x7b-instruct-hf",
    organization: "Hugging Face",
    name: "Mixtral MoE 8x7B Instruct (HF version)",
    maxLength: 32768,
  },
  "accounts/fireworks/models/mythomax-l2-13b": {
    id: "accounts/fireworks/models/mythomax-l2-13b",
    organization: "Gryphe",
    name: "MythoMax L2 13b",
    maxLength: 4096,
  },
  "accounts/fireworks/models/phi-3-vision-128k-instruct": {
    id: "accounts/fireworks/models/phi-3-vision-128k-instruct",
    organization: "Microsoft",
    name: "Phi 3.5 Vision Instruct",
    maxLength: 8192,
  },
  "accounts/fireworks/models/starcoder-16b": {
    id: "accounts/fireworks/models/starcoder-16b",
    organization: "BigCode",
    name: "StarCoder 15.5B",
    maxLength: 8192,
  },
  "accounts/fireworks/models/starcoder-7b": {
    id: "accounts/fireworks/models/starcoder-7b",
    organization: "BigCode",
    name: "StarCoder 7B",
    maxLength: 8192,
  },
  "accounts/fireworks/models/yi-01-ai/yi-large": {
    id: "accounts/fireworks/models/yi-01-ai/yi-large",
    organization: "01.AI",
    name: "Yi-Large",
    maxLength: 32768,
  },
};

module.exports.MODELS = MODELS;
