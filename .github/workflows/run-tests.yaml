name: Run backend tests

concurrency:
  group: build-${{ github.ref }}
  cancel-in-progress: true

on:
  pull_request:
    types: [opened, synchronize, reopened]
    paths:
      - "server/**.js"
      - "collector/**.js"

jobs:
  run-script:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v2

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Cache root dependencies
        uses: actions/cache@v3
        with:
          path: |
            node_modules
            ~/.cache/yarn
          key: ${{ runner.os }}-yarn-root-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-root-

      - name: Cache server dependencies
        uses: actions/cache@v3
        with:
          path: |
            server/node_modules
            ~/.cache/yarn
          key: ${{ runner.os }}-yarn-server-${{ hashFiles('server/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-server-

      - name: Cache collector dependencies
        uses: actions/cache@v3
        with:
          path: |
            collector/node_modules
            ~/.cache/yarn
          key: ${{ runner.os }}-yarn-collector-${{ hashFiles('collector/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-collector-

      - name: Install root dependencies
        if: steps.cache-root.outputs.cache-hit != 'true'
        run: yarn install --frozen-lockfile

      - name: Install server dependencies
        if: steps.cache-server.outputs.cache-hit != 'true'
        run: cd server && yarn install --frozen-lockfile

      - name: Install collector dependencies
        if: steps.cache-collector.outputs.cache-hit != 'true'
        run: cd collector && yarn install --frozen-lockfile

      - name: Setup environment and Prisma
        run: yarn setup:envs && yarn prisma:setup

      - name: Run test suites
        run: yarn test

      - name: Fail job on error
        if: failure()
        run: exit 1
