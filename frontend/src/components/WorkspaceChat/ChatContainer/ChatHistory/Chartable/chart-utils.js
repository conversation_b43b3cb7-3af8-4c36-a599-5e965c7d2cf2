export const Colors = {
  blue: "#3b82f6",
  sky: "#0ea5e9",
  cyan: "#06b6d4",
  teal: "#14b8a6",
  emerald: "#10b981",
  green: "#22c55e",
  lime: "#84cc16",
  yellow: "#eab308",
  amber: "#f59e0b",
  orange: "#f97316",
  red: "#ef4444",
  rose: "#f43f5e",
  pink: "#ec4899",
  fuchsia: "#d946ef",
  purple: "#a855f7",
  violet: "#8b5cf6",
  indigo: "#6366f1",
  neutral: "#737373",
  stone: "#78716c",
  gray: "#6b7280",
  slate: "#64748b",
  zinc: "#71717a",
};

export function getTremorColor(color) {
  switch (color) {
    case "blue":
      return Colors.blue;
    case "sky":
      return Colors.sky;
    case "cyan":
      return Colors.cyan;
    case "teal":
      return Colors.teal;
    case "emerald":
      return Colors.emerald;
    case "green":
      return Colors.green;
    case "lime":
      return Colors.lime;
    case "yellow":
      return Colors.yellow;
    case "amber":
      return Colors.amber;
    case "orange":
      return Colors.orange;
    case "red":
      return Colors.red;
    case "rose":
      return Colors.rose;
    case "pink":
      return Colors.pink;
    case "fuchsia":
      return Colors.fuchsia;
    case "purple":
      return Colors.purple;
    case "violet":
      return Colors.violet;
    case "indigo":
      return Colors.indigo;
    case "neutral":
      return Colors.neutral;
    case "stone":
      return Colors.stone;
    case "gray":
      return Colors.gray;
    case "slate":
      return Colors.slate;
    case "zinc":
      return Colors.zinc;
  }
}

export const themeColorRange = [
  "slate",
  "gray",
  "zinc",
  "neutral",
  "stone",
  "red",
  "orange",
  "amber",
  "yellow",
  "lime",
  "green",
  "emerald",
  "teal",
  "cyan",
  "sky",
  "blue",
  "indigo",
  "violet",
  "purple",
  "fuchsia",
  "pink",
  "rose",
];
